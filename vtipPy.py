#!/usr/bin/env python3
"""
VirusTotal IP Checker - A tool for checking IP addresses against VirusTotal's API.

This module provides functionality to check lists of IP addresses against the
VirusTotal public API while respecting rate limits and providing resume capability.
"""

import vt
import time
import logging
import ipaddress
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import sys
import os
import argparse


class VirusTotalIPChecker:
    """Main class for checking IP addresses against VirusTotal API."""

    DEFAULT_REQUESTS_PER_DAY = 500
    REQUESTS_PER_MINUTE = 4
    SLEEP_BETWEEN_REQUESTS = 15  # seconds

    def __init__(self, config_path: str = './.vtipPy.conf'):
        """Initialize the VirusTotal IP checker.

        Args:
            config_path: Path to the configuration file
        """
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        self.logger = self._setup_logging()

    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration."""
        logger = logging.getLogger('vtip')
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _get_current_time(self) -> str:
        """Get current time formatted as string."""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _save_config(self) -> None:
        """Save configuration to file."""
        self.config['conf_updated_time'] = self._get_current_time()

        try:
            with open(self.config_path, 'w') as file:
                json.dump(self.config, file, indent=2)
            self.logger.debug(f"Configuration saved to {self.config_path}")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
            raise

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default."""
        if self.config_path.exists():
            self.logger.info(f"Reading config from {self.config_path}")
            try:
                with open(self.config_path, 'r') as file:
                    config = json.load(file)
            except (json.JSONDecodeError, Exception) as e:
                self.logger.warning(f"Failed to load config: {e}. Creating new config.")
                config = self._create_default_config()
        else:
            self.logger.info(f"Creating new config at {self.config_path}")
            config = self._create_default_config()

        return config

    def _create_default_config(self) -> Dict[str, Any]:
        """Create default configuration."""
        return {
            'api_key': '',
            'requests_started_at': self._get_current_time(),
            'available_requests': self.DEFAULT_REQUESTS_PER_DAY,
            'voted_malicious': [],
            'detected_malicious': [],
            'detected_suspicious': []
        }

    def _update_available_requests(self) -> None:
        """Update available requests based on time elapsed."""
        try:
            requests_started_at = datetime.strptime(
                self.config['requests_started_at'],
                "%Y-%m-%d %H:%M:%S"
            )
            time_since_first_request = datetime.now() - requests_started_at

            if time_since_first_request > timedelta(days=1):
                self.logger.info("More than 24 hours since API usage began. Resetting available requests.")
                self.config['available_requests'] = self.DEFAULT_REQUESTS_PER_DAY
                self.config['requests_started_at'] = self._get_current_time()

        except (ValueError, KeyError) as e:
            self.logger.warning(f"Error updating available requests: {e}. Resetting.")
            self.config['available_requests'] = self.DEFAULT_REQUESTS_PER_DAY
            self.config['requests_started_at'] = self._get_current_time()

    def _validate_ip(self, ip_string: str) -> bool:
        """Validate if string is a valid IPv4 address.

        Args:
            ip_string: String to validate

        Returns:
            True if valid IPv4 address, False otherwise
        """
        try:
            ipaddress.IPv4Address(ip_string.strip())
            return True
        except ipaddress.AddressValueError:
            return False

    def _load_ip_list(self, file_path: str) -> List[str]:
        """Load and validate IP addresses from file.

        Args:
            file_path: Path to file containing IP addresses

        Returns:
            List of valid IP addresses

        Raises:
            FileNotFoundError: If file doesn't exist
            ValueError: If file is empty or contains no valid IPs
        """
        file_path_obj = Path(file_path)
        if not file_path_obj.exists():
            raise FileNotFoundError(f"Input file not found: {file_path}")

        try:
            with open(file_path_obj, 'r') as file:
                lines = file.read().strip().split('\n')
        except Exception as e:
            raise ValueError(f"Failed to read file {file_path}: {e}")

        # Filter out empty lines and validate IPs
        valid_ips = []
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:  # Skip empty lines
                continue
            if line.lower() == 'ipaddress':  # Skip header if present
                continue

            if self._validate_ip(line):
                valid_ips.append(line)
            else:
                self.logger.warning(f"Invalid IP address on line {line_num}: '{line}' - skipping")

        if not valid_ips:
            raise ValueError(f"No valid IP addresses found in {file_path}")

        self.logger.info(f"Loaded {len(valid_ips)} valid IP addresses from {file_path}")
        return valid_ips

    def _get_api_key(self) -> str:
        """Get API key from user if not configured.

        Returns:
            API key string
        """
        if not self.config.get('api_key'):
            print("# To start checking IPs against the VirusTotal API, you need to provide your VirusTotal API key.")
            print("# If you don't know how to get one, start here: https://docs.virustotal.com/docs/please-give-me-an-api-key")
            print()
            print("# After you input your API key once per project directory, it will remain saved in the config file")

            api_key = input("# Paste your API key here: ").strip()
            if not api_key:
                raise ValueError("API key cannot be empty")

            self.config['api_key'] = api_key
            self._save_config()

        return self.config['api_key']

    def _check_single_ip(self, ip: str, api_key: str) -> Optional[Dict[str, Any]]:
        """Check a single IP address against VirusTotal.

        Args:
            ip: IP address to check
            api_key: VirusTotal API key

        Returns:
            Dictionary with IP analysis results or None if failed
        """
        try:
            with vt.Client(api_key) as client:
                ip_obj = client.get_object(f"/ip_addresses/{ip}")

                result = {
                    'ip': ip,
                    'total_votes': ip_obj.total_votes,
                    'last_analysis_stats': ip_obj.last_analysis_stats
                }

                # Track malicious and suspicious IPs
                if ip_obj.total_votes.get('malicious', 0) > 0:
                    if ip not in self.config['voted_malicious']:
                        self.config['voted_malicious'].append(ip)

                if ip_obj.last_analysis_stats.get('malicious', 0) > 0:
                    if ip not in self.config['detected_malicious']:
                        self.config['detected_malicious'].append(ip)

                if ip_obj.last_analysis_stats.get('suspicious', 0) > 0:
                    if ip not in self.config['detected_suspicious']:
                        self.config['detected_suspicious'].append(ip)

                return result

        except Exception as e:
            self.logger.error(f"Failed to check IP {ip}: {e}")
            return None

    def _write_result(self, result: Dict[str, Any], output_file: str, quiet: bool = False) -> None:
        """Write result to output file and optionally print to console.

        Args:
            result: Result dictionary from IP check
            output_file: Path to output file
            quiet: Whether to suppress console output
        """
        output_line = f"{result['ip']}, {result['total_votes']}, {result['last_analysis_stats']}\n"

        try:
            with open(output_file, 'a') as file:
                file.write(output_line)
        except Exception as e:
            self.logger.error(f"Failed to write to output file: {e}")
            raise

        if not quiet:
            print(output_line.strip())

    def initialize(self) -> None:
        """Initialize the checker by loading configuration."""
        self.config = self._load_config()
        self._update_available_requests()
        self._save_config()


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        prog='vtipPy',
        description="Easily check IP addresses against VirusTotal's public API",
        epilog=(
            "Examples:\n"
            "  vtipPy.py -f /path/to/input/file\n"
            "  vtipPy.py --resume -f /path/to/input/file\n"
            "  vtipPy.py -f ips.txt -o custom_output.txt"
        ),
        formatter_class=argparse.RawTextHelpFormatter
    )

    parser.add_argument(
        '-f', '--file',
        help='Specify an input file containing IP addresses (one per line)',
        required=True
    )

    parser.add_argument(
        '-o', '--output',
        help='Specify a name for an output file (default: inputname_out.txt)'
    )

    parser.add_argument(
        '-r', '--resume',
        help='Resume a scan from where it left off',
        action='store_true'
    )

    parser.add_argument(
        '-F', '--force',
        help='Skip confirmations for overwrites',
        action='store_true'
    )

    parser.add_argument(
        '-q', '--quiet',
        help='Suppress most output (excluding confirmations)',
        action='store_true'
    )

    args = parser.parse_args()

    # Validate input file exists
    if not os.path.isfile(args.file):
        parser.error(f"Input file not found: {args.file}")

    # Set default output filename if not provided
    if args.output is None:
        base, _ = os.path.splitext(os.path.basename(args.file))
        args.output = f"{base}_out.txt"

    return args


def main():
    """Main function to run the VirusTotal IP checker."""
    try:
        args = parse_arguments()
        checker = VirusTotalIPChecker()
        checker.initialize()

        # Get API key
        api_key = checker._get_api_key()

        # Load IP addresses
        try:
            ips = checker._load_ip_list(args.file)
        except (FileNotFoundError, ValueError) as e:
            print(f"Error: {e}")
            return 1

        available_requests = checker.config['available_requests']
        print(f"# You have {available_requests} requests remaining until tomorrow")
        print(f"# Input file contains {len(ips)} valid IP addresses.")

        if len(ips) > checker.DEFAULT_REQUESTS_PER_DAY:
            print("# Due to the daily limit of requests on the public VirusTotal API, your job won't be able to finish today.")
            print("# Run the program again tomorrow with the --resume flag")

        # Handle resume logic
        resume_key = f'checked_in_{args.file}'
        start_index = 0

        if not args.resume:
            # Check if resume data exists
            if resume_key in checker.config and not args.force:
                print(f"# WARNING: You are about to start a scan over for {args.file} even though the scan may be able to be resumed.")
                print("# To resume the previous scan, answer NO and rerun the command with -r")
                print("# Starting the scan over will overwrite any existing output files from the previous scan.")
                choice = input("# Would you like to start the scan over? Y/N: ").strip().lower()

                if choice in ['y', 'yes']:
                    print("# Starting scan over from scratch...")
                    checker.config[resume_key] = 0
                    # Clear output file
                    if os.path.isfile(args.output):
                        open(args.output, 'w').close()
                else:
                    print("# Exiting...")
                    return 0
            else:
                checker.config[resume_key] = 0
        else:
            # Resume mode
            if resume_key in checker.config:
                start_index = checker.config[resume_key]
                print(f"# Resuming scan starting from IP address #{start_index + 1} in {args.file}")
                ips = ips[start_index:]
            else:
                print(f"# WARNING: There is no resume data for {args.file} in the configuration file.")
                print("# Exiting...")
                return 1

        # Save config after resume logic
        checker._save_config()

        # Process IPs
        processed_count = start_index

        for ip in ips:
            if available_requests < 1:
                print("# Daily limit reached. You can start again tomorrow by adding -r to the same command")
                print("# Your place will be saved in the config.")
                break

            result = checker._check_single_ip(ip, api_key)
            if result:
                checker._write_result(result, args.output, args.quiet)
                available_requests -= 1
                processed_count += 1

                # Update config with progress
                checker.config['available_requests'] = available_requests
                checker.config[resume_key] = processed_count
                checker._save_config()

                # Rate limiting
                time.sleep(checker.SLEEP_BETWEEN_REQUESTS)
            else:
                # Log error but continue with next IP
                with open('vt_ip_errors.txt', 'a') as error_file:
                    error_file.write(f"Failed to process IP: {ip}\n")

        print(f"# Scan completed. Processed {processed_count - start_index} IP addresses.")
        return 0

    except KeyboardInterrupt:
        print("\n# Scan interrupted by user. Progress has been saved.")
        return 0
    except Exception as e:
        print(f"# Fatal error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())